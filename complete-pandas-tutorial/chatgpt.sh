#!/bin/zsh

# 从终端或快捷指令传入参数
if [ -z "$1" ]; then
  read -p "You: " user_input
else
  user_input="$1"
fi

# 调用 OpenAI API（记得替换成你自己的 API Key）
response=$(curl -s https://api.openai.com/v1/chat/completions \
  -H "Authorization: Bearer ********************************************************************************************************************************************************************
" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "'"$user_input"'"}]
  }')

# 输出回复
echo ""
echo "ChatGPT:"
echo "$response" | jq -r '.choices[0].message.content'
